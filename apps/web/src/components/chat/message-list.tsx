import { UIMessage } from "ai";

import Card<PERSON>ool<PERSON>all from "@/web/components/chat/card-tool-call";
import Message from "@/web/components/chat/message";
import { cn } from "@/web/lib/utils";

interface MessageListProps {
  messages: UIMessage[];
  addToolResult: (obj: {
    toolCallId: string;
    result: { action: "continue" | "abort" };
  }) => void;
  className?: string;
}

// TODO: have this come from the backend
const HUMAN_REVIEW_REQUIRED_TOOLS = [
  "update_opportunity",
  "update_account",
  "create_contact",
  "update_contact",
  "create_task",
  "update_task",
  "create_event",
  "update_event",
];

export default function MessageList({
  messages,
  addToolResult,
  className,
}: MessageListProps) {
  return (
    <div className={cn("w-full max-w-3xl space-y-4 p-4 pb-32", className)}>
      {messages.map((message) => (
        <div key={message.id}>
          {message.parts.map((part, index) => {
            if (part.type === "text") {
              return (
                <Message key={index} content={part.text} role={message.role} />
              );
            } else if (
              part.type === "tool-invocation" &&
              HUMAN_REVIEW_REQUIRED_TOOLS.includes(part.toolInvocation.toolName)
            ) {
              return (
                <CardToolCall
                  key={index}
                  toolInvocation={part.toolInvocation}
                  addToolResult={addToolResult}
                />
              );
            }
            return null; // TODO: Handle other part types if needed
          })}
        </div>
      ))}
    </div>
  );
}
